# 测试样本类型识别修复功能
# 用于验证样本类型识别是否正常工作

source("utils/data_index_manager.R")

# 测试用例
test_files <- c(
  "QC_N_4_13.raw",
  "BLANK_001.raw", 
  "STD_20240617.raw",
  "SAMPLE_001.raw",
  "calib_stand_001.raw",
  "test_blank_sample.raw",
  "QC_20240617.raw",
  "unknown_sample.raw",
  "STANDARD_REF.raw",
  "BLANK_CTRL.raw"
)

cat("测试样本类型识别功能:\n")
cat("="*50, "\n")

for (file in test_files) {
  sample_type <- detect_sample_type(file)
  cat(sprintf("%-25s -> %s\n", file, sample_type))
}

cat("\n")
cat("扫描模式识别测试:\n")
cat("="*50, "\n")

test_paths <- c(
  "D:/data/negative/QC_001.raw",
  "D:/data/positive/STD_001.raw",
  "D:/data/neg/SAMPLE_001.raw",
  "D:/data/pos/BLANK_001.raw",
  "D:/data/QC_POS_001.raw",
  "D:/data/QC_NEG_001.raw"
)

for (path in test_paths) {
  scan_mode <- detect_scan_mode(path)
  cat(sprintf("%-35s -> %s\n", basename(path), scan_mode))
}