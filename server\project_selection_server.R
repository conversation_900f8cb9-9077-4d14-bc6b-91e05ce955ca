# 项目选择页面服务器逻辑

# 项目状态管理
project_active <- reactiveVal(FALSE)
current_project_info <- reactiveVal(NULL)

# 项目信息更新触发器（如果不存在则创建）
if (!exists("project_info_updated")) {
  project_info_updated <- reactiveVal(0)
}

# 检查是否有活动项目
output$has_active_project <- reactive({
  project_active()
})
outputOptions(output, "has_active_project", suspendWhenHidden = FALSE)

# 当前项目信息显示
output$current_project_info <- renderText({
  tryCatch({
    project_root <- get_project_root_path()
    
    if (is.null(project_root)) {
      return("当前没有活动项目\n请选择新建项目或导入现有项目")
    }
    
    # 尝试读取项目配置文件
    config_file <- get_project_config_path()
    
    if (file.exists(config_file)) {
      config <- jsonlite::fromJSON(config_file)
      
      info_text <- paste(
        paste("项目名称:", config$name %||% "未知"),
        paste("项目路径:", config$path %||% project_root),
        paste("创建时间:", config$created_time %||% "未知"),
        paste("项目描述:", config$description %||% "无描述"),
        paste("项目状态:", config$status %||% "未知"),
        paste("配置文件:", config_file),
        paste("当前工作目录:", getwd()),
        sep = "\n"
      )
      
      return(info_text)
    } else {
      return(paste(
        "项目路径:", project_root,
        "配置文件不存在，请检查项目完整性",
        paste("当前工作目录:", getwd()),
        sep = "\n"
      ))
    }
  }, error = function(e) {
    return(paste("获取项目信息失败:", e$message))
  })
})

# 新建项目按钮事件
observeEvent(input$create_new_project_btn, {
  # 显示新建项目模态框的逻辑已在UI中处理
})

# 导入项目按钮事件
observeEvent(input$import_existing_project_btn, {
  # 显示导入项目模态框的逻辑已在UI中处理
})

# 确认创建项目
observeEvent(input$confirm_create_project, {
  project_name <- input$new_project_name
  project_description <- input$new_project_description
  custom_path <- if (input$project_path_type == "custom") input$custom_project_path else NULL

  if (is.null(project_name) || project_name == "") {
    showEnhancedNotification("warning", "输入错误", "请输入项目名称", duration = 3000)
    return()
  }

  safeExecute({
    # 创建项目
    result <- create_project(project_name, project_description, custom_path)

    if (result$success) {
      # 设置项目为活动状态
      project_active(TRUE)
      current_project_info(result$project)

      # 更新项目信息显示
      project_info_text <- paste(
        paste("项目名称:", result$project$name),
        paste("项目路径:", result$project$path),
        paste("创建时间:", result$project$created_time),
        paste("项目描述:", result$project$description %||% "无描述"),
        paste("项目状态:", result$project$status),
        sep = "\n"
      )

      runjs(paste0("
        var indicator = document.getElementById('project_status_indicator');
        var statusText = document.getElementById('project_status_text');
        var projectInfo = document.getElementById('current_project_info');

        if (indicator) indicator.className = 'status-indicator active';
        if (statusText) statusText.textContent = '项目已打开: ", project_name, "';
        if (projectInfo) projectInfo.textContent = '", gsub("'", "\\\\'", project_info_text), "';
      "))

      # 触发项目信息更新
      if (exists("project_info_updated")) {
        project_info_updated(project_info_updated() + 1)
      }

      showEnhancedNotification("success", "项目创建成功",
                              paste("项目", project_name, "已成功创建，可以进入工作区"), duration = 3000)

    } else {
      showEnhancedNotification("error", "创建失败", result$message, duration = 5000)
    }
  }, error_title = "项目创建失败")
})

# 验证导入路径
observeEvent(input$validate_import_path, {
  import_path <- input$import_project_path
  
  if (is.null(import_path) || import_path == "") {
    showEnhancedNotification("warning", "路径错误", "请输入项目文件夹路径", duration = 3000)
    return()
  }
  
  tryCatch({
    if (!dir.exists(import_path)) {
      runjs("
        var validationInfo = document.getElementById('import_validation_info');
        var validationMessage = document.getElementById('validation_message');
        validationInfo.style.display = 'block';
        validationInfo.style.backgroundColor = '#f8d7da';
        validationInfo.style.borderColor = '#f5c6cb';
        validationMessage.textContent = '路径不存在，请检查路径是否正确';
        document.getElementById('confirm_import_project').disabled = true;
      ")
      return()
    }
    
    # 检查项目结构
    config_dir <- file.path(import_path, "config")
    if (!dir.exists(config_dir)) {
      runjs("
        var validationInfo = document.getElementById('import_validation_info');
        var validationMessage = document.getElementById('validation_message');
        validationInfo.style.display = 'block';
        validationInfo.style.backgroundColor = '#fff3cd';
        validationInfo.style.borderColor = '#ffeaa7';
        validationMessage.textContent = '缺少config目录，不是有效的项目文件夹';
        document.getElementById('confirm_import_project').disabled = true;
      ")
      return()
    }
    
    # 检查配置文件
    config_files <- list.files(config_dir, pattern = "\\.json$")
    if (length(config_files) == 0) {
      runjs("
        var validationInfo = document.getElementById('import_validation_info');
        var validationMessage = document.getElementById('validation_message');
        validationInfo.style.display = 'block';
        validationInfo.style.backgroundColor = '#fff3cd';
        validationInfo.style.borderColor = '#ffeaa7';
        validationMessage.textContent = '缺少项目配置文件(.json)';
        document.getElementById('confirm_import_project').disabled = true;
      ")
      return()
    }
    
    # 验证成功
    runjs("
      var validationInfo = document.getElementById('import_validation_info');
      var validationMessage = document.getElementById('validation_message');
      validationInfo.style.display = 'block';
      validationInfo.style.backgroundColor = '#d4edda';
      validationInfo.style.borderColor = '#c3e6cb';
      validationMessage.textContent = '项目验证成功，可以导入';
      document.getElementById('confirm_import_project').disabled = false;
    ")
    
  }, error = function(e) {
    runjs(paste0("
      var validationInfo = document.getElementById('import_validation_info');
      var validationMessage = document.getElementById('validation_message');
      validationInfo.style.display = 'block';
      validationInfo.style.backgroundColor = '#f8d7da';
      validationInfo.style.borderColor = '#f5c6cb';
      validationMessage.textContent = '验证失败: ", e$message, "';
      document.getElementById('confirm_import_project').disabled = true;
    "))
  })
})

# 确认导入项目
observeEvent(input$confirm_import_project, {
  import_path <- input$import_project_path

  if (is.null(import_path) || import_path == "") {
    showEnhancedNotification("warning", "路径错误", "请输入项目文件夹路径", duration = 3000)
    return()
  }

  safeExecute({
    # 导入项目
    result <- import_project(import_path)

    if (result$success) {
      # 设置项目为活动状态
      project_active(TRUE)
      current_project_info(result$project)

      # 更新项目信息显示
      project_info_text <- paste(
        paste("项目名称:", result$project$name),
        paste("项目路径:", result$project$path),
        paste("导入时间:", result$project$import_time %||% format(Sys.time(), "%Y-%m-%d %H:%M:%S")),
        paste("项目描述:", result$project$description %||% "无描述"),
        paste("项目状态:", result$project$status),
        paste("导入类型:", result$project$import_type %||% "external"),
        sep = "\n"
      )

      runjs(paste0("
        var indicator = document.getElementById('project_status_indicator');
        var statusText = document.getElementById('project_status_text');
        var projectInfo = document.getElementById('current_project_info');

        if (indicator) indicator.className = 'status-indicator active';
        if (statusText) statusText.textContent = '项目已打开: ", result$project$name, "';
        if (projectInfo) projectInfo.textContent = '", gsub("'", "\\\\'", project_info_text), "';
      "))

      # 触发项目信息更新
      if (exists("project_info_updated")) {
        project_info_updated(project_info_updated() + 1)
      }

      showEnhancedNotification("success", "项目导入成功",
                              paste("项目", result$project$name, "已成功导入，可以进入工作区"), duration = 3000)

    } else {
      showEnhancedNotification("error", "导入失败", result$message, duration = 5000)
    }
  }, error_title = "项目导入失败")
})

# 进入工作区按钮已删除，现在使用自动进入工作区功能

# 关闭项目
observeEvent(input$close_project_btn, {
  project_active(FALSE)
  current_project_info(NULL)

  # 切换回项目选择页面
  shinyjs::runjs("
    document.getElementById('workspace_page').style.display = 'none';
    document.getElementById('project_selection_page').style.display = 'block';

    var indicator = document.getElementById('project_status_indicator');
    var statusText = document.getElementById('project_status_text');
    var projectInfo = document.getElementById('current_project_info');

    if (indicator) indicator.className = 'status-indicator';
    if (statusText) statusText.textContent = '未打开项目';
    if (projectInfo) projectInfo.textContent = '当前没有活动项目\\n请选择新建项目或导入现有项目';
  ")

  showEnhancedNotification("info", "项目已关闭", "已返回项目选择页面", duration = 2000)
})

# 项目设置
observeEvent(input$project_settings_btn, {
  # 显示项目映射信息
  tryCatch({
    projects_map <- load_projects_map()

    if (length(projects_map) > 0) {
      map_info <- paste(
        "项目路径映射:",
        paste(names(projects_map), "->", unlist(projects_map), collapse = "\n"),
        sep = "\n"
      )
    } else {
      map_info <- "当前没有项目路径映射"
    }

    showEnhancedNotification("info", "项目映射信息", map_info, duration = 5000)
  }, error = function(e) {
    showEnhancedNotification("error", "获取映射失败", e$message, duration = 3000)
  })
})
