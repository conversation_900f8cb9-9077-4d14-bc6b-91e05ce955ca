# 测试样本类型识别功能
source("utils/data_index_manager.R")

# 测试文件名样本类型识别
test_files <- c(
  "QC_sample_001.raw",
  "blank_test.mzML",
  "Standard_calib.mzXML",
  "sample_001.mgf",
  "QC_20241201.raw",
  "BLANK_001.d",
  "STD_100ug.mzML",
  "unknown_sample.raw"
)

cat("=== 样本类型识别测试 ===\n")
for (file in test_files) {
  sample_type <- detect_sample_type(file)
  cat(sprintf("文件: %-25s 样本类型: %s\n", file, sample_type))
}

# 测试扫描模式识别
test_paths <- c(
  "data/positive/sample_001.raw",
  "data/negative/QC_sample.raw",
  "data/pos/Standard_calib.mzML",
  "data/neg/blank_test.mzXML",
  "data/positive_mode/test.mgf",
  "data/negative_mode/QC_001.d"
)

cat("\n=== 扫描模式识别测试 ===\n")
for (path in test_paths) {
  scan_mode <- detect_scan_mode(path)
  cat(sprintf("路径: %-35s 扫描模式: %s\n", path, scan_mode))
}