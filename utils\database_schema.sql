-- 质谱数据质量控制系统数据库schema
-- 基于JSON schema设计的SQLite数据库结构
-- 支持高效查询和存储质谱数据及质控结果

-- 文件元数据表
CREATE TABLE IF NOT EXISTS files (
    file_id TEXT PRIMARY KEY,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size TEXT,
    file_type TEXT CHECK(file_type IN ('Thermo RAW', 'mzML', 'mzXML', 'MGF', 'Agilent .d')),
    sample_type TEXT CHECK(sample_type IN ('QC', 'STD', 'BLANK', 'SAMPLE')),
    scan_mode TEXT CHECK(scan_mode IN ('positive', 'negative', 'Unknown')),
    acquisition_time DATETIME,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    processing_status TEXT CHECK(processing_status IN ('待处理', '处理中', '已处理', '失败')),
    notes TEXT,
    metadata_json TEXT,  -- 存储额外元数据的JSON字符串
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 谱图元数据表
CREATE TABLE IF NOT EXISTS spectra (
    spectrum_id TEXT PRIMARY KEY,
    file_id TEXT NOT NULL,
    scan_index INTEGER,
    ms_level INTEGER,
    retention_time REAL,
    polarity TEXT CHECK(polarity IN ('positive', 'negative')),
    precursor_mz REAL,
    precursor_intensity REAL,
    precursor_charge INTEGER,
    collision_energy REAL,
    isolation_window_target REAL,
    isolation_window_lower REAL,
    isolation_window_upper REAL,
    peaks_count INTEGER,
    total_ion_current REAL,
    base_peak_mz REAL,
    base_peak_intensity REAL,
    injection_time REAL,
    filter_string TEXT,
    low_mz REAL,
    high_mz REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (file_id) REFERENCES files(file_id) ON DELETE CASCADE
);

-- 峰数据表（采用压缩存储）
CREATE TABLE IF NOT EXISTS peak_data (
    peak_id TEXT PRIMARY KEY,
    spectrum_id TEXT NOT NULL,
    mz_array TEXT NOT NULL,  -- JSON格式的m/z数组
    intensity_array TEXT NOT NULL,  -- JSON格式的强度数组
    min_mz REAL,
    max_mz REAL,
    min_intensity REAL,
    max_intensity REAL,
    total_peaks INTEGER,
    compression_type TEXT DEFAULT 'json',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (spectrum_id) REFERENCES spectra(spectrum_id) ON DELETE CASCADE
);

-- 质控指标表
CREATE TABLE IF NOT EXISTS qc_metrics (
    metric_id TEXT PRIMARY KEY,
    file_id TEXT NOT NULL,
    analysis_type TEXT CHECK(analysis_type IN ('isotope_internal_standard', 'distributed_internal_standard', 'qc_monitoring', 'ms2_monitoring', 'file_diagnosis')),
    analysis_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 通用指标
    total_ion_chromatogram TEXT,  -- JSON格式的TIC数据
    base_peak_intensity TEXT,     -- JSON格式的BPI数据
    baseline_noise REAL,
    ms2_frequency INTEGER,
    precursor_accuracy REAL,
    
    -- 质量标志
    data_quality TEXT CHECK(data_quality IN ('excellent', 'good', 'acceptable', 'poor')),
    instrument_status TEXT CHECK(instrument_status IN ('normal', 'warning', 'critical')),
    
    -- 异常值列表（JSON格式）
    outliers TEXT,
    
    -- 完整质控数据（JSON格式）
    full_metrics_json TEXT NOT NULL,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (file_id) REFERENCES files(file_id) ON DELETE CASCADE
);

-- 同位素内标监控表
CREATE TABLE IF NOT EXISTS internal_standard_monitoring (
    monitor_id TEXT PRIMARY KEY,
    file_id TEXT NOT NULL,
    compound_name TEXT NOT NULL,
    theoretical_mz REAL,
    expected_rt REAL,
    ion_type TEXT,
    
    -- 测量数据
    peak_area REAL,
    peak_height REAL,
    measured_rt REAL,
    measured_mz REAL,
    half_peak_width REAL,
    sn_ratio REAL,
    peak_shape_score REAL,
    
    -- MS2碎片数据（JSON格式）
    ms2_fragments TEXT,
    
    -- 稳定性指标
    rt_cv REAL,
    intensity_cv REAL,
    mz_accuracy REAL,
    
    -- 质量标志
    rt_stability TEXT CHECK(rt_stability IN ('excellent', 'good', 'acceptable', 'poor')),
    intensity_stability TEXT CHECK(intensity_stability IN ('excellent', 'good', 'acceptable', 'poor')),
    peak_shape_quality TEXT CHECK(peak_shape_quality IN ('excellent', 'good', 'acceptable', 'poor')),
    
    -- 原始数据引用
    eic_data_id TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (file_id) REFERENCES files(file_id) ON DELETE CASCADE
);

-- 分布式内标监控表
CREATE TABLE IF NOT EXISTS distributed_standard_monitoring (
    monitor_id TEXT PRIMARY KEY,
    file_id TEXT NOT NULL,
    
    -- 识别结果
    identified_standards_count INTEGER,
    identification_rate REAL,
    false_positive_rate REAL,
    
    -- Pool QC对齐结果
    rt_correlation REAL,
    intensity_correlation REAL,
    peak_overlap_ratio REAL,
    
    -- 质量评估
    quality_score REAL,
    quality_flags TEXT,  -- JSON格式
    
    -- 分析详情（JSON格式）
    analysis_details TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (file_id) REFERENCES files(file_id) ON DELETE CASCADE
);

-- 批处理状态表
CREATE TABLE IF NOT EXISTS processing_status (
    process_id TEXT PRIMARY KEY,
    file_id TEXT NOT NULL,
    process_type TEXT NOT NULL,  -- 'extraction', 'qc_analysis', 'monitoring'
    status TEXT CHECK(status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    progress REAL DEFAULT 0.0,
    started_at DATETIME,
    completed_at DATETIME,
    error_message TEXT,
    worker_info TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (file_id) REFERENCES files(file_id) ON DELETE CASCADE
);

-- 缓存表（用于存储预计算的汇总数据）
CREATE TABLE IF NOT EXISTS cache_summary (
    cache_id TEXT PRIMARY KEY,
    cache_type TEXT NOT NULL,  -- 'file_list', 'qc_summary', 'monitoring_summary'
    project_name TEXT,
    cache_data TEXT NOT NULL,  -- JSON格式
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME,
    is_valid BOOLEAN DEFAULT TRUE
);

-- 触发器更新updated_at字段
CREATE TRIGGER IF NOT EXISTS update_files_timestamp 
AFTER UPDATE ON files
BEGIN
    UPDATE files SET updated_at = CURRENT_TIMESTAMP WHERE file_id = NEW.file_id;
END;

CREATE TRIGGER IF NOT EXISTS update_qc_metrics_timestamp 
AFTER UPDATE ON qc_metrics
BEGIN
    UPDATE qc_metrics SET updated_at = CURRENT_TIMESTAMP WHERE metric_id = NEW.metric_id;
END;

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_files_sample_type ON files(sample_type);
CREATE INDEX IF NOT EXISTS idx_files_scan_mode ON files(scan_mode);
CREATE INDEX IF NOT EXISTS idx_files_status ON files(processing_status);
CREATE INDEX IF NOT EXISTS idx_files_created ON files(created_at);

CREATE INDEX IF NOT EXISTS idx_spectra_file_id ON spectra(file_id);
CREATE INDEX IF NOT EXISTS idx_spectra_ms_level ON spectra(ms_level);
CREATE INDEX IF NOT EXISTS idx_spectra_retention_time ON spectra(retention_time);
CREATE INDEX IF NOT EXISTS idx_spectra_precursor_mz ON spectra(precursor_mz);

CREATE INDEX IF NOT EXISTS idx_peak_data_spectrum ON peak_data(spectrum_id);

CREATE INDEX IF NOT EXISTS idx_qc_metrics_file ON qc_metrics(file_id);
CREATE INDEX IF NOT EXISTS idx_qc_metrics_type ON qc_metrics(analysis_type);
CREATE INDEX IF NOT EXISTS idx_qc_metrics_time ON qc_metrics(analysis_time);

CREATE INDEX IF NOT EXISTS idx_is_monitoring_file ON internal_standard_monitoring(file_id);
CREATE INDEX IF NOT EXISTS idx_is_monitoring_compound ON internal_standard_monitoring(compound_name);

CREATE INDEX IF NOT EXISTS idx_ds_monitoring_file ON distributed_standard_monitoring(file_id);

CREATE INDEX IF NOT EXISTS idx_processing_file ON processing_status(file_id);
CREATE INDEX IF NOT EXISTS idx_processing_status ON processing_status(status);

CREATE INDEX IF NOT EXISTS idx_cache_type ON cache_summary(cache_type, project_name);

-- 视图：文件统计汇总
CREATE VIEW IF NOT EXISTS file_statistics AS
SELECT 
    f.file_id,
    f.file_name,
    f.sample_type,
    f.scan_mode,
    COUNT(s.spectrum_id) as total_spectra,
    COUNT(CASE WHEN s.ms_level = 1 THEN 1 END) as ms1_spectra,
    COUNT(CASE WHEN s.ms_level = 2 THEN 1 END) as ms2_spectra,
    MIN(s.retention_time) as min_rt,
    MAX(s.retention_time) as max_rt,
    AVG(s.total_ion_current) as avg_tic,
    f.processing_status,
    f.created_at
FROM files f
LEFT JOIN spectra s ON f.file_id = s.file_id
GROUP BY f.file_id;

-- 视图：质控汇总
CREATE VIEW IF NOT EXISTS qc_summary AS
SELECT 
    f.file_id,
    f.file_name,
    f.sample_type,
    qm.analysis_type,
    qm.data_quality,
    qm.instrument_status,
    qm.analysis_time,
    julianday('now') - julianday(qm.analysis_time) as days_since_analysis
FROM files f
LEFT JOIN qc_metrics qm ON f.file_id = qm.file_id
WHERE qm.analysis_type IN ('qc_monitoring', 'file_diagnosis');

-- 视图：内标监控汇总
CREATE VIEW IF NOT EXISTS internal_standard_summary AS
SELECT 
    f.file_id,
    f.file_name,
    COUNT(DISTINCT ism.compound_name) as monitored_compounds,
    AVG(ism.rt_cv) as avg_rt_cv,
    AVG(ism.intensity_cv) as avg_intensity_cv,
    AVG(ism.mz_accuracy) as avg_mass_accuracy,
    GROUP_CONCAT(CASE WHEN ism.rt_stability = 'poor' THEN ism.compound_name END) as rt_issues,
    GROUP_CONCAT(CASE WHEN ism.intensity_stability = 'poor' THEN ism.compound_name END) as intensity_issues
FROM files f
LEFT JOIN internal_standard_monitoring ism ON f.file_id = ism.file_id
WHERE f.sample_type = 'QC'
GROUP BY f.file_id;